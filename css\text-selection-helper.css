/**
 * Pagetalk - Text Selection Helper Styles
 * 划词助手样式
 */

/* 基础样式重置 */
.pagetalk-selection-helper * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Mini Icon 样式 (改为 absolute 定位) */
.pagetalk-mini-icon {
    position: absolute; /* 改动：从 fixed 改为 absolute */
    z-index: 2147483646;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    user-select: none;
}

.pagetalk-mini-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.pagetalk-mini-icon img {
    width: 20px;
    height: 20px;
    pointer-events: none;
}

/* 选项栏样式 (改为 absolute 定位) */
.pagetalk-options-bar {
    position: absolute; /* 改动：从 fixed 改为 absolute */
    z-index: 2147483647;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    user-select: none;
}

.pagetalk-options-bar-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 4px;
}

.pagetalk-options-bar-icon img {
    width: 16px;
    height: 16px;
}

.pagetalk-option {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
}

.pagetalk-option:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.pagetalk-option-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    color: #666;
}

.pagetalk-option-text {
    font-weight: 500;
}

/* 功能窗口样式 */
.pagetalk-function-window {
    position: fixed;
    z-index: 2147483647;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
    resize: both;
    min-width: 400px; /* 保留最小宽度 */
    min-height: 250px; /* 保留最小高度 */
    /* 移除 max-width 限制，由 JavaScript 动态控制 */
    width: 480px; /* 设置默认宽度 */
    height: 300px; /* 设置默认高度，由 JavaScript 动态调整 */
    transition: width 0.3s ease, height 0.3s ease; /* 添加宽度和高度变化动画 */
}

/* --- Start of Fix: Scoped CSS variables and styles --- */
/*
 * Injected CSS Variables for Text Selection Helper.
 * These variables mimic the main panel's theme to ensure consistent styling
 * for markdown and code blocks, which are rendered in the host page's DOM.
 */
.pagetalk-function-window {
    /* Light Mode Variables (Default) */
    --primary-color: #4674ff;
    --text-color: #343a40e5;
    --text-secondary: #868E96;
    --border-color: #E9ECEF;
    --code-background: #F1F3F5;
    --code-text-color: #343A40;
    --code-border: #DEE2E6;
    --table-header-bg: #F1F3F5;
    --table-even-row-bg: #F8F9FA;
    --link-color: var(--primary-color);
    --blockquote-bg: rgba(175, 184, 193, 0.1);
    --blockquote-text: #6a737d;
    --inline-code-bg: rgba(175, 184, 193, 0.2);
    --inline-code-text: #24292e;
    --inline-code-border: rgba(175, 184, 193, 0.2);
    /* 圆角变量 */
    --radius-full: 9999px;
}



/*
 * Scoped Markdown and Code Highlighting Styles.
 * All selectors are prefixed with `.pagetalk-function-window` to prevent conflicts
 * with the host webpage's styles. These styles are copied and adapted from
 * _markdown.css and github.min.css (light theme only).
 */

/* Scoped Markdown Styles from _markdown.css */
.pagetalk-function-window .markdown-rendered h1,
.pagetalk-function-window .markdown-rendered h2,
.pagetalk-function-window .markdown-rendered h3,
.pagetalk-function-window .markdown-rendered h4,
.pagetalk-function-window .markdown-rendered h5,
.pagetalk-function-window .markdown-rendered h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    line-height: 1.3;
    color: var(--text-color);
}

.pagetalk-function-window .markdown-rendered ul,
.pagetalk-function-window .markdown-rendered ol {
    margin: 0.6em 0;
    padding-left: 1.8em;
}

.pagetalk-function-window .markdown-rendered li {
    margin-bottom: 0.4em;
}

/* 基础段落样式 - 与主面板保持一致 */
.pagetalk-function-window .markdown-rendered p {
    margin-top: 0;
    margin-bottom: 0.6em; /* 与主面板 .bot-message p 保持一致的段落间距 */
    line-height: 1.4;
}

.pagetalk-function-window .markdown-rendered p:last-child {
    margin-bottom: 0;
}

.pagetalk-function-window .markdown-rendered p + p::before {
    content: none;
    display: none;
    height: 0;
}

.pagetalk-function-window .markdown-rendered li p {
    margin: 0.3em 0;
}

.pagetalk-function-window .markdown-rendered p + ul,
.pagetalk-function-window .markdown-rendered p + ol {
    margin-top: 0.4em;
}

.pagetalk-function-window .markdown-rendered ul + p,
.pagetalk-function-window .markdown-rendered ol + p {
    margin-top: 0.6em;
}

.pagetalk-function-window .markdown-rendered h1 + p,
.pagetalk-function-window .markdown-rendered h2 + p,
.pagetalk-function-window .markdown-rendered h3 + p,
.pagetalk-function-window .markdown-rendered h4 + p {
    margin-top: 0.3em;
}

.pagetalk-function-window .markdown-rendered .table-container {
    width: 100%;
    overflow-x: auto;
    margin: 0.8em 0;
    border-radius: 4px; /* --radius-sm */
    border: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagetalk-function-window .markdown-rendered table {
    border-collapse: collapse;
    margin: 0;
    width: 100%;
    font-size: 0.9em;
}

.pagetalk-function-window .markdown-rendered thead {
    background-color: var(--table-header-bg);
    color: var(--text-color);
    text-align: left;
}


.pagetalk-function-window .markdown-rendered th {
    font-weight: 600;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    text-align: left;
    background-color: var(--table-header-bg);
    white-space: pre-wrap;
}

.pagetalk-function-window .markdown-rendered td {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    white-space: pre-wrap;
}

.pagetalk-function-window .markdown-rendered tr:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

.pagetalk-function-window .markdown-rendered tr:nth-child(even) {
    background-color: var(--table-even-row-bg);
}

.pagetalk-function-window .markdown-rendered pre {
    background-color: var(--code-background);
    border-radius: 4px; /* --radius-sm */
    padding: 0.8em 1em;
    margin: 0.8em 0;
    overflow-x: auto;
    border: 1px solid var(--code-border);
    position: relative;
    border-left: 3px solid var(--primary-color);
}

.pagetalk-function-window .markdown-rendered pre code {
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    border-radius: 0 !important;
    display: block;
    line-height: 1.4;
    color: var(--code-text-color);
    font-family: Consolas, 'SFMono-Regular', 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 1em;
    white-space: pre-wrap;
    word-break: break-word;
}

.pagetalk-function-window .markdown-rendered a {
    color: var(--link-color);
    text-decoration: underline;
}

.pagetalk-function-window .markdown-rendered a:hover {
    text-decoration: none;
}

.pagetalk-function-window .markdown-rendered blockquote {
    border-left: 4px solid var(--primary-color);
    margin: 0.7em 0;
    padding: 0.4em 0.8em;
    color: var(--blockquote-text);
    background-color: var(--blockquote-bg);
}

.pagetalk-function-window .markdown-rendered blockquote > :first-child {
    margin-top: 0;
}

.pagetalk-function-window .markdown-rendered blockquote > :last-child {
    margin-bottom: 0;
}

.pagetalk-function-window .markdown-rendered hr {
    height: 1px;
    padding: 0;
    margin: 1em 0;
    background-color: var(--border-color);
    border: 0;
}

.pagetalk-function-window .markdown-rendered code:not(pre code) {
    background-color: var(--inline-code-bg);
    border-radius: 4px;
    font-family: Consolas, 'SFMono-Regular', 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 0.9em;
    padding: 0.15em 0.3em;
    margin: 0 0.1em;
    border: 1px solid var(--inline-code-border);
    color: var(--inline-code-text);
    white-space: break-spaces;
    word-break: break-all;
}

/* Scoped Code Highlighting Styles (支持深色模式) */
.pagetalk-function-window pre code.hljs { display: block; overflow-x: auto; padding: 1em; }
.pagetalk-function-window code.hljs { padding: 3px 5px; }
.pagetalk-function-window .hljs { color: var(--code-text-color); background: var(--code-background); }
.pagetalk-function-window .hljs-doctag, .pagetalk-function-window .hljs-keyword, .pagetalk-function-window .hljs-meta .hljs-keyword, .pagetalk-function-window .hljs-template-tag, .pagetalk-function-window .hljs-template-variable, .pagetalk-function-window .hljs-type, .pagetalk-function-window .hljs-variable.language_ { color: #d73a49; }
.pagetalk-function-window .hljs-title, .pagetalk-function-window .hljs-title.class_, .pagetalk-function-window .hljs-title.class_.inherited__, .pagetalk-function-window .hljs-title.function_ { color: #6f42c1; }
.pagetalk-function-window .hljs-attr, .pagetalk-function-window .hljs-attribute, .pagetalk-function-window .hljs-literal, .pagetalk-function-window .hljs-meta, .pagetalk-function-window .hljs-number, .pagetalk-function-window .hljs-operator, .pagetalk-function-window .hljs-variable, .pagetalk-function-window .hljs-selector-attr, .pagetalk-function-window .hljs-selector-class, .pagetalk-function-window .hljs-selector-id { color: #005cc5; }
.pagetalk-function-window .hljs-regexp, .pagetalk-function-window .hljs-string, .pagetalk-function-window .hljs-meta .hljs-string { color: #032f62; }
.pagetalk-function-window .hljs-built_in, .pagetalk-function-window .hljs-symbol { color: #e36209; }
.pagetalk-function-window .hljs-code, .pagetalk-function-window .hljs-comment, .pagetalk-function-window .hljs-formula { color: #6a737d; }
.pagetalk-function-window .hljs-name, .pagetalk-function-window .hljs-quote, .pagetalk-function-window .hljs-selector-tag, .pagetalk-function-window .hljs-selector-pseudo { color: #22863a; }
.pagetalk-function-window .hljs-subst { color: #24292e; }
.pagetalk-function-window .hljs-section { color: #005cc5; font-weight: bold; }
.pagetalk-function-window .hljs-bullet { color: #735c0f; }
.pagetalk-function-window .hljs-emphasis { color: #24292e; font-style: italic; }
.pagetalk-function-window .hljs-strong { color: #24292e; font-weight: bold; }
.pagetalk-function-window .hljs-addition { color: #22863a; background-color: var(--success-bg, #f0fff4); }
.pagetalk-function-window .hljs-deletion { color: #b31d28; background-color: var(--error-bg, #ffeef0); }




/* --- End of Fix --- */

/* 窗口头部 */
.pagetalk-window-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.5);
    cursor: move;
}

.pagetalk-window-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.pagetalk-window-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagetalk-window-controls select {
    padding: 4px 8px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    cursor: pointer;
}

.pagetalk-window-controls button {
    display: flex;
    align-items: center;     /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;
    padding: 0; /* 新增：移除可能导致偏移的内边距 */
}

.pagetalk-window-controls button:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.pagetalk-window-close { /* 确保通用关闭按钮也居中 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    line-height: 1 !important; /* 确保行高不会影响垂直对齐 */
}

.pagetalk-window-drag-handle {
    flex: 1;
    height: 20px;
    cursor: move;
}

/* 引用区域 */
.pagetalk-quote-area {
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
}

.pagetalk-quote-text {
    font-style: italic;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    padding-left: 12px;
    word-wrap: break-word;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.pagetalk-quote-text.collapsed {
    max-height: 2.8em; /* 约2行的高度 (1.4 * 2) */
    position: relative;
}

/* 为折叠状态添加渐变遮罩，避免文字与展开按钮重叠 */
.pagetalk-quote-text.collapsed::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 140px; /* 增加宽度，确保完全覆盖按钮区域 */
    height: 1.6em; /* 稍微增加高度，确保覆盖完整 */
    background: linear-gradient(to right,
        transparent 0%,
        transparent 10%,
        rgba(252, 252, 252, 0.2) 20%,
        rgba(252, 252, 252, 0.5) 35%,
        rgba(252, 252, 252, 0.8) 50%,
        rgba(252, 252, 252, 0.95) 65%,
        rgba(252, 252, 252, 1) 80%,
        rgba(252, 252, 252, 1) 100%
    );
    pointer-events: none; /* 不阻挡点击事件 */
    z-index: 2; /* 提高层级，确保在文字上方 */
}

.pagetalk-quote-text.expanded {
    max-height: none;
}

.pagetalk-quote-text::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #007AFF;
    border-radius: 2px;
}

.pagetalk-quote-toggle {
    position: absolute;
    bottom: 6px;
    right: 10px;
    background: rgba(0, 122, 255, 0.12);
    border: 1px solid rgba(0, 122, 255, 0.4);
    border-radius: 14px;
    padding: 3px 10px;
    font-size: 11px;
    font-weight: 500;
    color: #007AFF;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    z-index: 15; /* 提高层级，确保在渐变遮罩之上 */
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.1);
    backdrop-filter: blur(4px); /* 添加毛玻璃效果 */
}

.pagetalk-quote-toggle:hover {
    background: rgba(0, 122, 255, 0.18);
    border-color: rgba(0, 122, 255, 0.6);
    box-shadow: 0 2px 6px rgba(0, 122, 255, 0.15);
    transform: translateY(-0.5px);
}

/* 响应区域 */
.pagetalk-response-area {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.pagetalk-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    font-size: 14px;
}

.pagetalk-response-content {
    flex: 1;
    line-height: 1.5;
    color: #333;
    font-size: 14px;
    /* 移除 white-space: pre-wrap; 以支持正确的markdown渲染 */
    word-wrap: break-word;
}

.pagetalk-response-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.pagetalk-response-actions button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    color: #666;
}

.pagetalk-response-actions button:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #333;
}

/* 错误样式 */
.pagetalk-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 12px;
}

.pagetalk-error-message {
    color: var(--error-color, #ff4444);
    font-size: 14px;
    text-align: center;
}

.pagetalk-retry-btn {
    padding: 8px 16px;
    border: 1px solid var(--error-color, #ff4444);
    border-radius: 6px;
    background: rgba(255, 68, 68, 0.1);
    color: var(--error-color, #ff4444);
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagetalk-retry-btn:hover {
    background: rgba(255, 68, 68, 0.2);
}

/* 聊天消息区域 */
.pagetalk-chat-messages {
    flex: 1;
    padding: 16px 16px 24px 16px; /* 增加底部padding从16px到24px，为助手消息按钮留出空间 */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px; /* 增加消息间距，为用户消息下方的按钮留出更多空间 */
}

.pagetalk-chat-message {
    display: flex;
    flex-direction: column;
    gap: 4px;
    position: relative; /* 为按钮定位提供参考 */
}

.pagetalk-chat-message-user {
    align-items: flex-end;
    margin-bottom: 24px; /* 为用户消息下方的按钮留出空间 */
}

.pagetalk-chat-message-assistant {
    align-items: flex-start;
    width: 100%; /* 新增：确保助手消息的容器占满整行宽度 */
}

.pagetalk-message-content {
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
    width: fit-content; /* 让气泡宽度由内容决定 */
    position: relative; /* 为按钮定位提供参考 */
}

.pagetalk-chat-message-user .pagetalk-message-content {
    background: #007AFF;
    color: white;
    border-bottom-right-radius: 4px;
    padding: 8px 12px; /* 用户消息保持原有padding */
}

.pagetalk-chat-message-assistant .pagetalk-message-content {
    background: var(--card-background);
    color: var(--text-color);
    border-bottom-left-radius: 4px;
    border: 1px solid var(--border-color);
    padding: 12px; /* 标准内边距 */
    padding-bottom: 30px; /* 底部增加padding为按钮留出空间 */
    max-width: 95%;
    min-width: 120px; /* 设置最小宽度确保按钮有足够空间 */
}

/* 旧的思考动画样式已移除，现在使用独立的思考动画 */

.pagetalk-message-actions {
    position: absolute;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(2px) scale(0.9);
    z-index: 5;
}

/* 用户消息按钮位置：气泡下方 */
.pagetalk-chat-message-user .pagetalk-message-actions {
    bottom: -24px;
    right: 0;
}

/* 助手消息按钮位置：在气泡内容内部的右下角 */
.pagetalk-chat-message-assistant .pagetalk-message-content .pagetalk-message-actions {
    bottom: 6px; /* 对应主面板的 var(--spacing-xs) */
    right: 6px; /* 对应主面板的 var(--spacing-xs) */
}

.pagetalk-chat-message:hover .pagetalk-message-actions,
.pagetalk-message-content:hover .pagetalk-message-actions {
    opacity: 1;
    transform: translateY(2px) scale(1);
}

.pagetalk-message-actions button {
    width: 24px;
    height: 24px;
    border-radius: 50%; /* 完全圆形 */
    background-color: rgba(255, 255, 255, 0.9); /* 浅色模式背景 */
    border: none; /* 确保无边框 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
    color: #888; /* 默认灰色 */
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.pagetalk-message-actions button:hover {
    transform: scale(1.1);
    color: var(--primary-color); /* Default hover to primary color */
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagetalk-message-actions button:active {
    transform: scale(0.95);
}

/* 用户消息内的按钮特定背景颜色 */
.pagetalk-user-message .pagetalk-message-actions button {
    background-color: rgba(255, 255, 255, 0.3);
    color: rgba(104, 103, 103, 0.8);
}

.pagetalk-user-message .pagetalk-message-actions button:hover {
    background-color: rgba(255, 255, 255, 0.6);
    color: var(--primary-color); /* User message action buttons also hover to primary */
}

/* 删除按钮特殊样式 */
.pagetalk-message-actions .pagetalk-delete-btn:hover {
    color: #dc3545 !important; /* 删除按钮悬停时显示红色 */
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.pagetalk-user-message .pagetalk-message-actions .pagetalk-delete-btn:hover {
    color: #dc3545 !important; /* 用户消息中的删除按钮也显示红色 */
    background-color: rgba(220, 53, 69, 0.15) !important;
}

/* 聊天输入区域 */
.pagetalk-chat-input {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.5);
}

.pagetalk-chat-input textarea {
    flex: 1;
    height: 36px; /* 固定高度，与发送按钮同高 */
    min-height: 36px;
    max-height: 36px; /* 限制最大高度 */
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 18px; /* 增加圆角，配合发送按钮 */
    background: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-family: inherit;
    resize: none;
    outline: none;
    line-height: 1.2; /* 调整行高 */
}

.pagetalk-chat-input textarea:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.pagetalk-send-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%; /* 改为圆形 */
    background: #007AFF;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0; /* 防止按钮被压缩 */
}

.pagetalk-send-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.pagetalk-send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 流式输出暂停按钮样式 */
.pagetalk-send-btn.pagetalk-stop-streaming {
    background: #007AFF; /* 保持蓝色背景 */
    transform: none; /* 重置悬停变换 */
}

.pagetalk-send-btn.pagetalk-stop-streaming:hover {
    background: #DC3545; /* 悬停时变成红色 */
    color: white;
    transform: scale(1.05);
}

/* 独立思考动画样式 - 完全复制主面板的样式 */
.pagetalk-function-window .pagetalk-thinking-message {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* 左对齐，与助手消息一致 */
    margin: 8px 0;
    padding: 0;
}

.pagetalk-function-window .pagetalk-thinking-bubble {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px; /* 增加padding确保思考动画不会超出边界 */
    min-height: 40px; /* 使用主面板相同的高度 */
    min-width: 80px; /* 确保气泡足够宽以包含思考动画和光晕效果 */
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    border-bottom-left-radius: 4px; /* 与助手消息气泡一致 */
    animation: thinkingAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    position: relative;
    opacity: 1;
    transform: translateY(0) scale(1);
    max-width: 95%;
    width: fit-content; /* 让气泡根据内容调整大小 */
}

@keyframes thinkingAppear {
    from {
        opacity: 0;
        transform: translateY(8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.pagetalk-function-window .thinking-dots {
    display: flex;
    align-items: center;
    gap: 8px; /* 使用主面板相同的间距 */
    position: relative;
}

/* 添加背景光晕效果 */
.pagetalk-function-window .thinking-dots::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px; /* 使用主面板相同的尺寸 */
    height: 20px; /* 使用主面板相同的尺寸 */
    background: radial-gradient(ellipse, rgba(49, 123, 245, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    animation: thinkingGlow 2s infinite ease-in-out;
}

@keyframes thinkingGlow {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

.pagetalk-function-window .thinking-dots span {
    display: inline-block;
    width: 7px; /* 使用主面板相同的尺寸 */
    height: 7px; /* 使用主面板相同的尺寸 */
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), rgba(49, 123, 245, 0.7));
    opacity: 0.4;
    position: relative;
    z-index: 1;
    box-shadow: 0 1px 3px rgba(49, 123, 245, 0.2); /* 使用主面板相同的阴影 */
}

.pagetalk-function-window .thinking-dots span:nth-child(1) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0s;
}

.pagetalk-function-window .thinking-dots span:nth-child(2) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0.2s;
}

.pagetalk-function-window .thinking-dots span:nth-child(3) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0.4s;
}

@keyframes thinkingPulseAdvanced {
    0%, 60%, 100% {
        transform: scale(0.8) translateY(0);
        opacity: 0.4;
        box-shadow: 0 1px 3px rgba(49, 123, 245, 0.2);
    }
    30% {
        transform: scale(1.3) translateY(-2px); /* 使用主面板相同的动画参数 */
        opacity: 1;
        box-shadow: 0 3px 8px rgba(49, 123, 245, 0.4);
    }
}

/* 添加额外的微粒效果 */
.pagetalk-function-window .thinking-dots span::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    opacity: 0;
    animation: thinkingRipple 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
}

.pagetalk-function-window .thinking-dots span:nth-child(1)::after {
    animation-delay: 0s;
}

.pagetalk-function-window .thinking-dots span:nth-child(2)::after {
    animation-delay: 0.2s;
}

.pagetalk-function-window .thinking-dots span:nth-child(3)::after {
    animation-delay: 0.4s;
}

@keyframes thinkingRipple {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

/* 流式输出光标样式 */
.pagetalk-streaming-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: #007AFF;
    margin-left: 2px;
    animation: pagetalkStreamingPulse 1.2s infinite cubic-bezier(0.4, 0, 0.6, 1);
    vertical-align: text-bottom;
    border-radius: 1px;
}

@keyframes pagetalkStreamingPulse {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.4;
        transform: scaleY(0.8);
    }
}

/* 工具提示 */
.pagetalk-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 2147483648;
    pointer-events: none;
}



/* 划词助手开关样式 */
.text-selection-helper-toggle-container {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: var(--spacing-sm);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    cursor: pointer;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* 设置面板样式 - 主面板中的划词助手设置界面 */
#settings-text-selection-helper .setting-card {
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-md); /* 使用中等圆角，与 .agent-item 保持一致 */
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    width: calc(100% - 4px); /* 为阴影预留 2px 左右空间，与 .agent-item 保持一致 */
    margin: 2px 2px var(--spacing-md) 2px; /* 减少底部间距，使卡片更紧凑 */
    transform: scale(1);
    opacity: 1;
    overflow: hidden;
}

#settings-text-selection-helper .setting-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(76, 110, 245, 0.15);
    transform: translateY(-1px) scale(1.005);
    z-index: 1;
}

#settings-text-selection-helper .setting-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    cursor: pointer;
    transition: background-color 0.2s ease;
    background-color: transparent; /* 初始背景透明，与 .agent-item-header 保持一致 */
    border-bottom: 1px solid transparent;
}

/* 注意：header 不设置悬浮背景色变化，因为整个卡片已经有悬浮效果了 */

/* 设置面板样式 - 划词助手功能窗口内的设置界面 */
.pagetalk-function-window .setting-card {
    margin-bottom: 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.pagetalk-function-window .setting-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    cursor: pointer;
    transition: background-color 0.2s ease;
    background-color: transparent; /* 初始背景透明，与 .agent-item-header 保持一致 */
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.pagetalk-function-window .setting-card-header:hover {
    background-color: var(--button-hover-bg); /* Primary color transparent hover，与 .agent-item-header:hover 保持一致 */
}

#settings-text-selection-helper .setting-card-header h3 {
    margin: 0;
    font-size: clamp(12px, 3.5vw, 14px);
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    margin-right: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

#settings-text-selection-helper .setting-card-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: var(--primary-color);
}

#settings-text-selection-helper .setting-card-toggle {
    flex-shrink: 0;
    margin-left: auto;
    margin-right: var(--spacing-sm);
    color: var(--text-secondary);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
}

#settings-text-selection-helper .setting-card-toggle:hover {
    color: var(--text-color);
}

#settings-text-selection-helper .setting-card.collapsed .setting-card-toggle {
    transform: rotate(-90deg);
}

#settings-text-selection-helper .setting-card.expanded .setting-card-toggle {
    transform: rotate(0deg);
}

#settings-text-selection-helper .setting-card-content {
    max-height: 0;
    overflow: hidden;
    padding: 0 var(--spacing-lg);
    border-top: 1px solid transparent;
    background-color: var(--card-background); /* 使用主题变量，支持深色模式 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
}

#settings-text-selection-helper .setting-card.expanded .setting-card-content {
    max-height: 800px; /* 足够大的高度 */
    padding: var(--spacing-md) var(--spacing-lg);
    border-top-color: var(--border-color);
    opacity: 1;
}

#settings-text-selection-helper .setting-card.collapsed .setting-card-content {
    max-height: 0;
    padding: 0 var(--spacing-lg);
    border-top-color: transparent;
    opacity: 0;
}

/* 不可折叠的卡片样式（如选项顺序） */
#settings-text-selection-helper .setting-card.no-collapse .setting-card-header {
    cursor: default !important;
    pointer-events: none !important; /* 禁用所有点击事件 */
}

#settings-text-selection-helper .setting-card.no-collapse .setting-card-content {
    max-height: none !important;
    padding: var(--spacing-md) var(--spacing-lg) !important;
    border-top-color: var(--border-color) !important;
    opacity: 1 !important;
    display: block !important; /* 确保始终显示 */
}

.pagetalk-function-window .setting-card-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.pagetalk-function-window .setting-card-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    color: #666;
}

.pagetalk-function-window .setting-card-toggle:hover {
    color: #333;
}

.pagetalk-function-window .setting-card.collapsed .setting-card-toggle {
    transform: rotate(-90deg);
}

.pagetalk-function-window .setting-card-content {
    padding: 16px;
    display: block;
}

.pagetalk-function-window .setting-card.collapsed .setting-card-content {
    display: none;
}

#settings-text-selection-helper .setting-group {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
}

#settings-text-selection-helper .setting-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

#settings-text-selection-helper .setting-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: clamp(12px, 3vw, 13px);
    font-weight: 500;
    color: var(--text-color);
}

#settings-text-selection-helper .setting-group input[type="text"],
#settings-text-selection-helper .setting-group input[type="number"],
#settings-text-selection-helper .setting-group textarea,
#settings-text-selection-helper .setting-group select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg); /* 16px 圆角，与主面板保持一致 */
    font-size: clamp(12px, 3vw, 13px);
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--input-background);
    transform: translateY(0);
}

#settings-text-selection-helper .setting-group input:focus,
#settings-text-selection-helper .setting-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1), 0 2px 8px rgba(49, 123, 245, 0.05);
    background-color: var(--card-background);
    transform: translateY(-1px);
}

#settings-text-selection-helper .setting-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1);
    background-color: var(--card-background);
    transform: none; /* 取消选择框的上抬效果 */
}

.pagetalk-function-window .setting-group {
    margin-bottom: 16px;
}

.pagetalk-function-window .setting-group:last-child {
    margin-bottom: 0;
}

.pagetalk-function-window .setting-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.pagetalk-function-window .setting-group select,
.pagetalk-function-window .setting-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    font-family: inherit;
    outline: none;
    transition: border-color 0.2s ease;
}

.pagetalk-function-window .setting-group select:focus,
.pagetalk-function-window .setting-group textarea:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

#settings-text-selection-helper .setting-group textarea {
    min-height: 120px;
    resize: vertical;
}

#settings-text-selection-helper .temperature-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: var(--spacing-sm) 0;
}

#settings-text-selection-helper .temperature-control input[type="range"] {
    flex: 1;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: var(--border-color);
    border-radius: var(--radius-full);
    outline: none;
    position: relative;
    margin: 10px 0;
}

#settings-text-selection-helper .temperature-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--card-background);
    border: 2px solid var(--primary-color);
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-top: -6px;
}

#settings-text-selection-helper .temperature-control input[type="range"]::-webkit-slider-thumb:hover,
#settings-text-selection-helper .temperature-control input[type="range"]:active::-webkit-slider-thumb {
    transform: scale(1.2);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

#settings-text-selection-helper .temperature-control span {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
    font-size: clamp(12px, 3.5vw, 14px);
}

.pagetalk-function-window .setting-group textarea {
    resize: vertical;
    min-height: 60px;
}

.pagetalk-function-window .temperature-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pagetalk-function-window .temperature-control input[type="range"] {
    flex: 1;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.pagetalk-function-window .temperature-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #007AFF;
    border-radius: 50%;
    cursor: pointer;
}

.pagetalk-function-window .temperature-control input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #007AFF;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.pagetalk-function-window .temperature-value {
    font-size: 13px;
    font-weight: 500;
    color: #666;
    min-width: 30px;
    text-align: center;
}

.pagetalk-function-window .custom-options-list {
    margin-bottom: 16px;
}

.pagetalk-function-window .custom-option-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.5);
}

.pagetalk-function-window .custom-option-item:last-child {
    margin-bottom: 0;
}

.pagetalk-function-window .custom-option-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    color: #333;
}

.pagetalk-function-window .custom-option-actions {
    display: flex;
    gap: 4px;
}

.pagetalk-function-window .custom-option-actions button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;
}

.pagetalk-function-window .custom-option-actions button:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #333;
}

/* 自定义选项列表样式 */
#settings-text-selection-helper .custom-options-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

#settings-text-selection-helper .custom-option-item {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

#settings-text-selection-helper .custom-option-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(76, 110, 245, 0.1);
}

#settings-text-selection-helper .custom-option-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

#settings-text-selection-helper .custom-option-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

#settings-text-selection-helper .custom-option-actions {
    display: flex;
    gap: var(--spacing-md);
}

#settings-text-selection-helper .custom-option-actions button {
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: var(--text-secondary);
}

#settings-text-selection-helper .custom-option-actions button svg {
    width: 16px;
    height: 16px;
    transition: all 0.2s ease;
}

#settings-text-selection-helper .edit-custom-option-btn:hover {
    color: var(--primary-color);
    background-color: rgba(70, 116, 255, 0.1);
    transform: scale(1.1);
}

#settings-text-selection-helper .delete-custom-option-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    transform: scale(1.1);
}

#settings-text-selection-helper .custom-option-details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

#settings-text-selection-helper .custom-option-detail {
    display: flex;
    flex-direction: column;
}

#settings-text-selection-helper .custom-option-detail-label {
    font-weight: 500;
    margin-bottom: 2px;
}

#settings-text-selection-helper .custom-option-detail-value {
    color: var(--text-color);
}

#settings-text-selection-helper .add-custom-option-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
}

#settings-text-selection-helper .add-custom-option-btn:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(70, 116, 255, 0.3);
}

/* 自定义选项对话框样式 */
.custom-option-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.custom-option-dialog {
    background: var(--card-background);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
}

.custom-option-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.custom-option-dialog-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.custom-option-dialog-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    border-radius: 16px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.custom-option-dialog-close:hover {
    background-color: var(--button-hover-bg);
    color: var(--text-color);
    transform: scale(1.1);
}

.custom-option-dialog-content {
    padding: var(--spacing-lg);
    background: var(--card-background);
}

.custom-option-dialog-footer {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    justify-content: flex-end;
    background: var(--background-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.custom-option-dialog-cancel,
.custom-option-dialog-save {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.custom-option-dialog-cancel {
    background-color: var(--button-hover-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.custom-option-dialog-cancel:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-option-dialog-save {
    background-color: var(--primary-color);
    color: white;
}

.custom-option-dialog-save:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(70, 116, 255, 0.3);
}

/* 自定义选项对话框中的温度控制样式 */
.custom-option-dialog .temperature-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: var(--spacing-sm) 0;
}

.custom-option-dialog .temperature-control input[type="range"] {
    flex: 1;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: var(--border-color);
    border-radius: var(--radius-full);
    outline: none;
    position: relative;
    margin: 10px 0;
}

.custom-option-dialog .temperature-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--card-background);
    border: 2px solid var(--primary-color);
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-top: -6px;
}

.custom-option-dialog .temperature-control input[type="range"]::-webkit-slider-thumb:hover,
.custom-option-dialog .temperature-control input[type="range"]:active::-webkit-slider-thumb {
    transform: scale(1.2);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.custom-option-dialog .temperature-control .temperature-value {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
    font-size: clamp(12px, 3.5vw, 14px);
}

#settings-text-selection-helper .options-order-list {
    margin-bottom: var(--spacing-md);
}

#settings-text-selection-helper .order-option-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: 4px;
    background: var(--card-background);
    transition: all 0.2s ease;
    -webkit-user-select: none;
    user-select: none;
}

#settings-text-selection-helper .order-option-item:hover {
    background: var(--button-hover-bg);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#settings-text-selection-helper .order-option-item:last-child {
    margin-bottom: 0;
}

#settings-text-selection-helper .order-option-item.drag-over {
    background: rgba(76, 110, 245, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 110, 245, 0.2);
}

#settings-text-selection-helper .order-option-item[draggable="true"] {
    cursor: grab;
}

#settings-text-selection-helper .order-option-item[draggable="true"]:active {
    cursor: grabbing;
}

#settings-text-selection-helper .order-option-name {
    flex: 1;
    font-size: clamp(12px, 3vw, 13px);
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

#settings-text-selection-helper .order-option-icon {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    color: var(--primary-color);
}

#settings-text-selection-helper .order-option-type {
    font-size: clamp(10px, 2.5vw, 11px);
    color: var(--text-secondary);
    background: var(--button-hover-bg);
    padding: 2px 6px;
    border-radius: 3px;
}

#settings-text-selection-helper .hint {
    font-size: clamp(10px, 2.5vw, 11px);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    padding-left: var(--spacing-md);
    font-style: italic;
}

.pagetalk-function-window .options-order-list {
    margin-bottom: 16px;
}

.pagetalk-function-window .order-option-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    margin-bottom: 4px;
    background: rgba(255, 255, 255, 0.5);
    transition: all 0.2s ease;
    -webkit-user-select: none;
    user-select: none;
}

.pagetalk-function-window .order-option-item:hover {
    background: rgba(0, 0, 0, 0.02);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagetalk-function-window .order-option-item:last-child {
    margin-bottom: 0;
}

.pagetalk-function-window .order-option-item.drag-over {
    background: rgba(0, 122, 255, 0.1);
    border-color: #007AFF;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 122, 255, 0.2);
}

.pagetalk-function-window .order-option-item[draggable="true"] {
    cursor: grab;
}

.pagetalk-function-window .order-option-item[draggable="true"]:active {
    cursor: grabbing;
}

.pagetalk-function-window .order-option-name {
    flex: 1;
    font-size: 13px;
    color: #333;
}

.pagetalk-function-window .order-option-type {
    font-size: 11px;
    color: #666;
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 3px;
}

.pagetalk-function-window .hint {
    font-size: 12px;
    color: #666;
    margin: 0;
    font-style: italic;
}

/* ===== 深色模式适配 ===== */

/* 划词助手设置界面深色模式 */
body.dark-mode #settings-text-selection-helper .setting-card {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

body.dark-mode #settings-text-selection-helper .setting-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(116, 143, 252, 0.15);
}

body.dark-mode #settings-text-selection-helper .setting-card-header h3 {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .setting-card-icon {
    color: var(--primary-color);
}

body.dark-mode #settings-text-selection-helper .setting-card-toggle {
    color: var(--text-secondary);
}

body.dark-mode #settings-text-selection-helper .setting-card-toggle:hover {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .setting-card-content {
    background-color: var(--background-color); /* 使用主背景色，与深色模式保持一致 */
}

body.dark-mode #settings-text-selection-helper .setting-group label {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .setting-group input[type="text"],
body.dark-mode #settings-text-selection-helper .setting-group input[type="number"],
body.dark-mode #settings-text-selection-helper .setting-group textarea,
body.dark-mode #settings-text-selection-helper .setting-group select {
    background-color: var(--input-background);
    color: var(--text-color);
    border-color: var(--border-color);
}

body.dark-mode #settings-text-selection-helper .setting-group input:focus,
body.dark-mode #settings-text-selection-helper .setting-group textarea:focus,
body.dark-mode #settings-text-selection-helper .setting-group select:focus {
    background-color: var(--card-background);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(116, 143, 252, 0.2);
}

body.dark-mode #settings-text-selection-helper .temperature-control input[type="range"] {
    background: var(--border-color);
}

body.dark-mode #settings-text-selection-helper .temperature-control input[type="range"]::-webkit-slider-thumb {
    background-color: var(--input-background);
    border-color: var(--primary-color);
}

body.dark-mode #settings-text-selection-helper .temperature-control input[type="range"]::-webkit-slider-thumb:hover,
body.dark-mode #settings-text-selection-helper .temperature-control input[type="range"]:active::-webkit-slider-thumb {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

body.dark-mode #settings-text-selection-helper .temperature-control span {
    color: var(--primary-color);
}

body.dark-mode #settings-text-selection-helper .order-option-item {
    background: var(--card-background);
    border-color: var(--border-color);
}

body.dark-mode #settings-text-selection-helper .order-option-item:hover {
    background: var(--button-hover-bg);
}

body.dark-mode #settings-text-selection-helper .order-option-item.drag-over {
    background: rgba(116, 143, 252, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(116, 143, 252, 0.2);
}

body.dark-mode #settings-text-selection-helper .order-option-name {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .order-option-icon {
    color: var(--primary-color);
}

body.dark-mode #settings-text-selection-helper .order-option-type {
    color: var(--text-secondary);
    background: var(--button-hover-bg);
}

body.dark-mode #settings-text-selection-helper .hint {
    color: var(--text-secondary);
}

/* 深色模式下的自定义选项样式 */
body.dark-mode #settings-text-selection-helper .custom-option-item {
    background: var(--card-background);
    border-color: var(--border-color);
}

body.dark-mode #settings-text-selection-helper .custom-option-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(116, 143, 252, 0.15);
}

body.dark-mode #settings-text-selection-helper .custom-option-name {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .custom-option-detail-label {
    color: var(--text-secondary);
}

body.dark-mode #settings-text-selection-helper .custom-option-detail-value {
    color: var(--text-color);
}

body.dark-mode #settings-text-selection-helper .custom-option-actions button {
    color: var(--text-secondary);
    background-color: transparent;
}

body.dark-mode #settings-text-selection-helper .edit-custom-option-btn:hover {
    color: var(--primary-color);
    background-color: rgba(116, 143, 252, 0.15);
    transform: scale(1.1);
}

body.dark-mode #settings-text-selection-helper .delete-custom-option-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.15);
    transform: scale(1.1);
}

body.dark-mode #settings-text-selection-helper .add-custom-option-btn {
    background-color: var(--primary-color);
    color: white;
}

body.dark-mode #settings-text-selection-helper .add-custom-option-btn:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(116, 143, 252, 0.3);
}

/* 深色模式下的自定义选项对话框样式 */
body.dark-mode .custom-option-dialog {
    background: var(--card-background);
    color: var(--text-color);
    border-color: var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

body.dark-mode .custom-option-dialog-header {
    background: var(--background-color);
    border-bottom-color: var(--border-color);
}

body.dark-mode .custom-option-dialog-header h3 {
    color: var(--text-color);
}

body.dark-mode .custom-option-dialog-close:hover {
    background-color: var(--button-hover-bg);
    color: var(--text-color);
}

body.dark-mode .custom-option-dialog-content {
    background: var(--card-background);
    color: var(--text-color);
}

body.dark-mode .custom-option-dialog-content label {
    color: var(--text-color);
}

body.dark-mode .custom-option-dialog-content input,
body.dark-mode .custom-option-dialog-content select,
body.dark-mode .custom-option-dialog-content textarea {
    background: var(--input-background);
    color: var(--text-color);
    border-color: var(--border-color);
}

body.dark-mode .custom-option-dialog-content input:focus,
body.dark-mode .custom-option-dialog-content select:focus,
body.dark-mode .custom-option-dialog-content textarea:focus {
    background: var(--card-background);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(116, 143, 252, 0.2);
}

body.dark-mode .custom-option-dialog-footer {
    background: var(--background-color);
    border-top-color: var(--border-color);
}

body.dark-mode .custom-option-dialog-cancel {
    background-color: var(--button-hover-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

body.dark-mode .custom-option-dialog-cancel:hover {
    background-color: var(--border-color);
}

body.dark-mode .custom-option-dialog-save {
    background-color: var(--primary-color);
    color: white;
}

body.dark-mode .custom-option-dialog-save:hover {
    background-color: var(--secondary-color);
}

/* 深色模式下的温度控制样式 */
body.dark-mode .custom-option-dialog .temperature-control input[type="range"] {
    background: var(--border-color);
}

body.dark-mode .custom-option-dialog .temperature-control input[type="range"]::-webkit-slider-thumb {
    background-color: var(--input-background);
    border-color: var(--primary-color);
}

body.dark-mode .custom-option-dialog .temperature-control input[type="range"]::-webkit-slider-thumb:hover,
body.dark-mode .custom-option-dialog .temperature-control input[type="range"]:active::-webkit-slider-thumb {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

body.dark-mode .custom-option-dialog .temperature-control .temperature-value {
    color: var(--primary-color);
}

body.dark-mode .custom-option-dialog-close {
    color: var(--text-secondary) !important;
}

body.dark-mode .custom-option-dialog-close:hover {
    background-color: var(--button-hover-bg) !important;
    color: var(--text-color) !important;
}

body.dark-mode .custom-option-dialog-footer {
    border-top-color: var(--border-color) !important;
    background: var(--card-background) !important;
}

body.dark-mode .custom-option-dialog-cancel {
    background-color: var(--button-hover-bg) !important;
    color: var(--text-color) !important;
}

body.dark-mode .custom-option-dialog-cancel:hover {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

body.dark-mode .custom-option-dialog-save {
    background-color: var(--primary-color) !important;
    color: white !important;
}

body.dark-mode .custom-option-dialog-save:hover {
    background-color: var(--secondary-color);
    color: white;
    opacity: 1;
    visibility: visible;
}

/* 通用按钮悬浮修复 - 确保所有自定义选项相关按钮在悬浮时可见 */
#settings-text-selection-helper .custom-option-actions button:hover,
.custom-option-dialog button:hover,
#settings-text-selection-helper .add-custom-option-btn:hover {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

body.dark-mode #settings-text-selection-helper .custom-option-actions button:hover,
body.dark-mode .custom-option-dialog button:hover,
body.dark-mode #settings-text-selection-helper .add-custom-option-btn:hover {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}


/* ===== Mermaid 图表样式 ===== */

/* Mermaid 容器样式 */
.pagetalk-function-window .mermaid {
    margin: 1em 0;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 8px;
    overflow: hidden;
}

.pagetalk-function-window .mermaid:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagetalk-function-window .mermaid svg {
    max-width: 100%;
    height: auto;
    background-color: var(--card-background);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Mermaid 错误样式 */
.pagetalk-function-window .mermaid-error {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin: 1em 0;
    color: #dc3545;
    font-family: monospace;
    font-size: 14px;
    text-align: center;
}

/* Mermaid 模态框样式 */
.pagetalk-mermaid-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2147483648; /* 确保在划词助手之上 */
    padding: 30px;
    box-sizing: border-box;
    overflow: auto;
}

.pagetalk-mermaid-modal-content {
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    max-width: 95%;
    max-height: 90vh;
}

.pagetalk-mermaid-modal-content svg {
    max-width: 100%;
    max-height: 100%;
    height: auto;
    width: auto;
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    cursor: grab;
}

.pagetalk-mermaid-modal-content svg:active {
    cursor: grabbing;
}

/* Mermaid 模态框关闭按钮样式 */
.pagetalk-mermaid-close-modal {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2147483649; /* 确保关闭按钮在最上层 */
    line-height: 1;
    user-select: none;
}

.pagetalk-mermaid-close-modal:hover {
    color: #4674ff;
    text-decoration: none;
}

/* ===== KaTeX 数学公式样式 ===== */

/* KaTeX 基础样式 */
.pagetalk-function-window .katex {
    font-size: 1.1em;
    line-height: 1.2;
}

.pagetalk-function-window .katex-display {
    margin: 1em 0;
    text-align: center;
}

.pagetalk-function-window .katex-display > .katex {
    display: inline-block;
    white-space: nowrap;
}

/* KaTeX 错误样式 */
.pagetalk-function-window .katex-error {
    color: #cc0000;
    background-color: rgba(204, 0, 0, 0.1);
    border: 1px solid rgba(204, 0, 0, 0.3);
    border-radius: 4px;
    padding: 2px 4px;
    font-family: monospace;
    font-size: 0.9em;
}

/* 深色模式下的 Mermaid 和 KaTeX 样式 */
body.dark-mode .pagetalk-function-window .mermaid svg {
    background-color: var(--card-background);
}

body.dark-mode .pagetalk-mermaid-modal-content svg {
    background-color: var(--card-background, #2d3748);
    color: var(--text-color, #e2e8f0);
}

body.dark-mode .pagetalk-function-window .mermaid-error {
    background-color: rgba(255, 135, 135, 0.15);
    border-color: rgba(255, 135, 135, 0.3);
    color: var(--error-color);
}

body.dark-mode .pagetalk-function-window .katex-error {
    background-color: rgba(255, 135, 135, 0.15);
    border-color: rgba(255, 135, 135, 0.3);
    color: var(--error-color);
}

